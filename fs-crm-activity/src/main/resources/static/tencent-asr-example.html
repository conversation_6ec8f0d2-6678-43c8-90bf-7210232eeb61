<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腾讯云ASR WebSocket示例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .start { background-color: #4CAF50; color: white; }
        .stop { background-color: #f44336; color: white; }
        .result { border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 200px; }
        .log { background-color: #f9f9f9; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>腾讯云ASR WebSocket示例</h1>
        
        <div>
            <button id="startBtn" class="button start">开始录音</button>
            <button id="stopBtn" class="button stop" disabled>停止录音</button>
        </div>
        
        <div>
            <h3>识别结果：</h3>
            <div id="result" class="result"></div>
        </div>
        
        <div>
            <h3>连接日志：</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        class TencentASRClient {
            constructor() {
                this.ws = null;
                this.mediaRecorder = null;
                this.audioContext = null;
                this.isRecording = false;
                
                this.resultDiv = document.getElementById('result');
                this.logDiv = document.getElementById('log');
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                
                this.startBtn.addEventListener('click', () => this.startRecording());
                this.stopBtn.addEventListener('click', () => this.stopRecording());
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
                this.logDiv.scrollTop = this.logDiv.scrollHeight;
                console.log(message);
            }
            
            async startRecording() {
                try {
                    // 1. 调用后端API获取WSS URL
                    this.log('正在获取WSS URL...');
                    const response = await fetch('/api/activity_text/service/realtime_start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            objectId: 'demo-' + Date.now(),
                            sourceLanguage: 'cn',
                            diarizationEnabled: true,
                            translationEnabled: false,
                            provider: 'tencent'
                        })
                    });
                    
                    const result = await response.json();
                    if (!result.wsUrl) {
                        throw new Error('获取WSS URL失败');
                    }
                    
                    this.log(`获取到WSS URL: ${result.wsUrl.substring(0, 100)}...`);
                    this.taskId = result.taskId;
                    
                    // 2. 建立WebSocket连接
                    this.connectWebSocket(result.wsUrl);
                    
                    // 3. 开始录音
                    await this.startAudioCapture();
                    
                    this.startBtn.disabled = true;
                    this.stopBtn.disabled = false;
                    this.isRecording = true;
                    
                } catch (error) {
                    this.log(`启动录音失败: ${error.message}`);
                }
            }
            
            connectWebSocket(wsUrl) {
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = (event) => {
                    this.log('WebSocket连接已建立');
                };
                
                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleASRResult(data);
                    } catch (error) {
                        this.log(`解析消息失败: ${error.message}`);
                    }
                };
                
                this.ws.onerror = (error) => {
                    this.log(`WebSocket错误: ${error}`);
                };
                
                this.ws.onclose = (event) => {
                    this.log(`WebSocket连接已关闭: code=${event.code}, reason=${event.reason}`);
                };
            }
            
            handleASRResult(data) {
                if (data.code === 0 && data.result) {
                    const text = data.result.voice_text_str;
                    const sliceType = data.result.slice_type;
                    
                    if (sliceType === 2) {
                        // 稳定结果
                        this.log(`最终识别结果: ${text}`);
                        this.resultDiv.innerHTML += `<div><strong>最终:</strong> ${text}</div>`;
                    } else if (sliceType === 1) {
                        // 中间结果
                        this.log(`临时识别结果: ${text}`);
                        // 可以选择显示或不显示临时结果
                        // this.resultDiv.innerHTML += `<div style="color: #666;"><em>临时:</em> ${text}</div>`;
                    }
                } else if (data.code !== 0) {
                    this.log(`识别错误: code=${data.code}, message=${data.message}`);
                }
            }
            
            async startAudioCapture() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true
                        } 
                    });
                    
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                        sampleRate: 16000
                    });
                    
                    const source = this.audioContext.createMediaStreamSource(stream);
                    const processor = this.audioContext.createScriptProcessor(4096, 1, 1);
                    
                    processor.onaudioprocess = (event) => {
                        if (this.isRecording && this.ws && this.ws.readyState === WebSocket.OPEN) {
                            const inputBuffer = event.inputBuffer.getChannelData(0);
                            const pcmData = this.convertToPCM16(inputBuffer);
                            this.ws.send(pcmData);
                        }
                    };
                    
                    source.connect(processor);
                    processor.connect(this.audioContext.destination);
                    
                    this.stream = stream;
                    this.processor = processor;
                    
                    this.log('开始音频采集');
                    
                } catch (error) {
                    this.log(`音频采集失败: ${error.message}`);
                    throw error;
                }
            }
            
            convertToPCM16(float32Array) {
                const buffer = new ArrayBuffer(float32Array.length * 2);
                const view = new DataView(buffer);
                let offset = 0;
                
                for (let i = 0; i < float32Array.length; i++, offset += 2) {
                    const s = Math.max(-1, Math.min(1, float32Array[i]));
                    view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
                }
                
                return buffer;
            }
            
            async stopRecording() {
                try {
                    this.isRecording = false;
                    
                    // 停止音频采集
                    if (this.stream) {
                        this.stream.getTracks().forEach(track => track.stop());
                    }
                    if (this.processor) {
                        this.processor.disconnect();
                    }
                    if (this.audioContext) {
                        await this.audioContext.close();
                    }
                    
                    // 发送结束信号
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({type: "end"}));
                        this.ws.close();
                    }
                    
                    // 调用后端停止API
                    if (this.taskId) {
                        await fetch('/api/activity_text/service/realtime_stop', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                objectId: 'demo-' + Date.now(),
                                taskId: this.taskId,
                                provider: 'tencent'
                            })
                        });
                    }
                    
                    this.startBtn.disabled = false;
                    this.stopBtn.disabled = true;
                    
                    this.log('录音已停止');
                    
                } catch (error) {
                    this.log(`停止录音失败: ${error.message}`);
                }
            }
        }
        
        // 初始化客户端
        const asrClient = new TencentASRClient();
    </script>
</body>
</html>
