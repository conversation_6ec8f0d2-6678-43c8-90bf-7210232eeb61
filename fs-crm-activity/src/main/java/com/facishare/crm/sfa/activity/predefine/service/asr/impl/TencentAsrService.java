package com.facishare.crm.sfa.activity.predefine.service.asr.impl;

import com.facishare.crm.sfa.activity.predefine.service.asr.AbstractAsrService;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 腾讯云ASR服务实现
 *
 * <AUTHOR>
 * @date 2025/1/22
 * @description 基于腾讯云语音识别服务的ASR实现，直接拼接WSS链接
 */
@Component
@Slf4j
public class TencentAsrService extends AbstractAsrService {

    @Value("${tencent.asr.app-id:}")
    private String appId;

    @Value("${tencent.asr.secret-id:}")
    private String secretId;

    @Value("${tencent.asr.secret-key:}")
    private String secretKey;

    @Value("${tencent.asr.engine-model-type:16k_zh}")
    private String defaultEngineModelType;

    @Override
    public String getProviderName() {
        return "tencent";
    }

    @Override
    public TaskResult startRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStartArg arg) {
        TaskResult result = new TaskResult();
        result.setProvider(getProviderName());

        try {
            // 检查配置
            if (StringUtils.isBlank(appId) || StringUtils.isBlank(secretId) || StringUtils.isBlank(secretKey)) {
                throw new IllegalStateException("Tencent ASR service not properly configured");
            }

            // 生成唯一的voiceId
            String voiceId = UUID.randomUUID().toString();

            // 构建WSS URL
            String wsUrl = buildWebSocketUrl(arg, voiceId);

            // 设置返回结果
            result.setTaskId(voiceId);
            result.setWsUrl(wsUrl);

            log.info("Tencent ASR task created: taskId={}, wsUrl={}", voiceId, wsUrl);

            return result;

        } catch (Exception e) {
            log.error("Start Tencent realtime transcription failed", e);
            throw new RuntimeException("Failed to start Tencent ASR: " + e.getMessage(), e);
        }
    }

    /**
     * 构建腾讯云WebSocket URL
     * 按照官方文档握手阶段要求拼接参数和签名
     * 文档地址：https://cloud.tencent.com/document/product/1093/48982#835304ab-3b0b-487e-9daa-08f7086b4fdf
     */
    private String buildWebSocketUrl(ActivityText.ActivityRealtimeStartArg arg, String voiceId) {
        try {
            // 当前时间戳（秒）
            long timestamp = System.currentTimeMillis() / 1000;
            // 签名过期时间（90天后，但不能超过90天）
            long expired = timestamp + 90 * 24 * 3600;
            // 随机正整数，最长10位
            int nonce = (int) (Math.random() * 1000000000);

            // 引擎模型类型
            String engineModelType = mapLanguageToEngineModel(arg.getSourceLanguage());

            // 构建参数Map（按字典序排序）
            Map<String, String> params = new TreeMap<>();

            // 必选参数
            params.put("secretid", secretId);
            params.put("timestamp", String.valueOf(timestamp));
            params.put("expired", String.valueOf(expired));
            params.put("nonce", String.valueOf(nonce));
            params.put("engine_model_type", engineModelType);
            params.put("voice_id", voiceId);

            // 语音编码方式：1=pcm, 4=speex, 6=silk, 8=mp3, 10=opus, 12=wav, 14=m4a, 16=aac
            params.put("voice_format", "1"); // 默认PCM格式

            // 可选参数 - VAD（人声检测切分）
            if (arg.isDiarizationEnabled()) {
                params.put("needvad", "1");
            } else {
                params.put("needvad", "0");
            }

            // 可选参数 - 热词表（如果需要的话）
            // params.put("hotword_id", "your-hotword-id");

            // 可选参数 - 自学习模型（如果需要的话）
            // params.put("customization_id", "your-customization-id");

            // 可选参数 - 脏词过滤：0=不过滤, 1=过滤, 2=替换为*
            params.put("filter_dirty", "0");

            // 可选参数 - 语气词过滤：0=不过滤, 1=部分过滤, 2=严格过滤
            params.put("filter_modal", "0");

            // 可选参数 - 句末句号过滤：0=不过滤, 1=过滤
            params.put("filter_punc", "0");

            // 可选参数 - 空结果回调：0=回调空结果, 1=不回调空结果
            params.put("filter_empty_result", "1");

            // 可选参数 - 数字转换：0=不转换, 1=智能转换, 3=数学相关转换
            params.put("convert_num_mode", "1");

            // 可选参数 - 词级别时间戳：0=不显示, 1=显示不含标点, 2=显示含标点
            params.put("word_info", "0");

            // 可选参数 - 语音断句检测阈值（毫秒）
            params.put("vad_silence_time", "1000");

            // 可选参数 - 强制断句功能（毫秒）
            params.put("max_speak_time", "60000");

            // 可选参数 - 噪音参数阈值
            params.put("noise_threshold", "0");

            // 可选参数 - 临时热词表（如果需要的话）
            // params.put("hotword_list", "腾讯云|10,语音识别|5,ASR|11");

            // 可选参数 - PCM音频采样率适配
            // params.put("input_sample_rate", "8000");

            // 可选参数 - 情绪识别：0=不开启, 1=开启不显示, 2=开启并显示
            params.put("emotion_recognition", "0");

            // 添加翻译相关参数（如果启用翻译）
            if (arg.isTranslationEnabled() && StringUtils.isNotBlank(arg.getTargetLanguages())) {
                // 注意：翻译功能需要确认腾讯云是否支持，这里预留接口
                log.info("Translation enabled but not implemented for Tencent ASR yet");
            }

            // 添加自定义参数（根据业务需求）
            addCustomParameters(params, arg);

            // 构建签名原文（按照官方文档要求）
            // 1. 对除signature之外的所有参数按字典序排序
            // 2. 拼接请求URL（不包含协议部分wss://）作为签名原文
            StringBuilder signStr = new StringBuilder();
            signStr.append("asr.cloud.tencent.com/asr/v2/").append(appId).append("?");

            boolean first = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!first) {
                    signStr.append("&");
                }
                signStr.append(entry.getKey()).append("=").append(entry.getValue());
                first = false;
            }

            log.debug("Tencent ASR signature string: {}", signStr.toString());

            // 生成HMAC-SHA1签名并进行Base64编码
            String signature = generateSignature(signStr.toString(), secretKey);

            // 构建最终的WebSocket URL
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append("wss://asr.cloud.tencent.com/asr/v2/").append(appId).append("?");

            first = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                // 对参数值进行URL编码
                urlBuilder.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.name()));
                first = false;
            }

            // 添加签名参数（必须进行URL编码）
            urlBuilder.append("&signature=").append(URLEncoder.encode(signature, StandardCharsets.UTF_8.name()));

            String finalUrl = urlBuilder.toString();
            log.info("Generated Tencent ASR WebSocket URL: {}", finalUrl);

            return finalUrl;

        } catch (Exception e) {
            log.error("Failed to build WebSocket URL", e);
            throw new RuntimeException("Failed to build WebSocket URL: " + e.getMessage(), e);
        }
    }

    /**
     * 生成HMAC-SHA1签名
     * 按照腾讯云官方文档要求：
     * 1. 对签名原文使用SecretKey进行HMAC-SHA1加密
     * 2. 对加密结果进行Base64编码
     *
     * @param signStr 签名原文
     * @param secretKey 密钥
     * @return Base64编码的签名字符串
     */
    private String generateSignature(String signStr, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(signStr.getBytes(StandardCharsets.UTF_8));
        String signature = Base64.getEncoder().encodeToString(hash);

        log.debug("Generated signature: {}", signature);
        return signature;
    }

    /**
     * 根据业务需求添加自定义参数
     * 可以根据不同的场景添加特定的参数
     */
    private void addCustomParameters(Map<String, String> params, ActivityText.ActivityRealtimeStartArg arg) {
        // 根据对象ID或租户ID添加特定配置
        // 这里可以扩展更多的业务逻辑

        // 示例：根据语言类型优化参数
        String sourceLanguage = arg.getSourceLanguage();
        if ("en".equals(sourceLanguage) || "english".equals(sourceLanguage)) {
            // 英文场景的特殊配置
            params.put("filter_modal", "1"); // 英文可以适当过滤语气词
        } else if ("cn".equals(sourceLanguage) || "zh".equals(sourceLanguage)) {
            // 中文场景的特殊配置
            params.put("convert_num_mode", "1"); // 中文启用数字智能转换
        }

        // 示例：根据是否需要高精度识别调整参数
        if (arg.isDiarizationEnabled()) {
            // 启用说话人分离时，可能需要更长的静音检测时间
            params.put("vad_silence_time", "1500");
        }
    }

    @Override
    public Map<String, String> stopRealtimeTranscription(ServiceContext context, ActivityText.ActivityRealtimeStopArg arg) {
        Map<String, String> result = new HashMap<>();

        try {
            // 重置任务状态
            try {
                activityGeneralService.initEndRecordingState(context.getTenantId(),
                                                           context.getUser().getUserId(),
                                                           arg.getObjectId());
            } catch (Exception e) {
                log.error("initEndRecordingState error", e);
            }

            // 发送完成消息
            sendMQ(arg.getObjectId(), context.getTenantId(), context.getUser().getUserId(),
                   "realtime2textDone", "realtime2textDone", context.getLang().getValue());

            updateInteractiveProcesses(context.getUser(), arg.getObjectId());
            result.put("status", "success");

            log.info("Tencent ASR task stopped: taskId={}", arg.getTaskId());

        } catch (Exception e) {
            log.error("停止腾讯云实时转写任务失败", e);
            result.put("status", "error");
            result.put("message", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getServiceConfig(ServiceContext context) {
        Map<String, Object> config = new HashMap<>();
        config.put("provider", getProviderName());
        config.put("supportedLanguages", new String[]{"zh", "en", "ja", "ko", "th", "id", "vi", "ms", "fil", "pt", "tr", "ar", "es", "hi", "fr", "de"});
        config.put("supportedFormats", new String[]{"pcm", "wav", "opus", "speex", "silk", "mp3", "m4a", "aac"});
        config.put("maxConcurrency", 200);
        config.put("engineModels", new String[]{"16k_zh", "16k_zh_large", "16k_multi_lang", "16k_zh_en", "8k_zh", "8k_zh_large"});
        return config;
    }

    @Override
    protected boolean checkServiceDependencies(ServiceContext context) {
        return StringUtils.isNotBlank(appId) &&
               StringUtils.isNotBlank(secretId) &&
               StringUtils.isNotBlank(secretKey);
    }

    /**
     * 将语言映射到腾讯云引擎模型
     */
    private String mapLanguageToEngineModel(String sourceLanguage) {
        if (StringUtils.isBlank(sourceLanguage)) {
            return defaultEngineModelType;
        }
        
        switch (sourceLanguage.toLowerCase()) {
            case "cn":
            case "zh":
            case "chinese":
                return "16k_zh_large";
            case "en":
            case "english":
                return "16k_en";
            case "ja":
            case "japanese":
                return "16k_ja";
            case "ko":
            case "korean":
                return "16k_ko";
            case "yue":
            case "cantonese":
                return "16k_yue";
            default:
                return defaultEngineModelType;
        }
    }

    // 以下方法需要从原Activity2TextService中提取实现

    private void updateInteractiveProcesses(Object user, String objectId) {
        // 实现更新交互进程的逻辑
        log.debug("Update interactive processes for objectId: {}", objectId);
    }
}
