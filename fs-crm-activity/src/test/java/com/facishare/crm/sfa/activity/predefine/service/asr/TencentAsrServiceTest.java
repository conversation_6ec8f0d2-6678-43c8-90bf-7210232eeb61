package com.facishare.crm.sfa.activity.predefine.service.asr;

import com.facishare.crm.sfa.activity.predefine.service.asr.impl.TencentAsrService;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityText;
import com.facishare.paas.appframework.core.model.ServiceContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 腾讯云ASR服务测试
 * 
 * <AUTHOR>
 * @date 2025/1/22
 * @description 测试腾讯云ASR服务的WSS URL生成和基本功能
 */
@ExtendWith(MockitoExtension.class)
public class TencentAsrServiceTest {

    @InjectMocks
    private TencentAsrService tencentAsrService;

    @Mock
    private ServiceContext serviceContext;

    @BeforeEach
    public void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(tencentAsrService, "appId", "test-app-id");
        ReflectionTestUtils.setField(tencentAsrService, "secretId", "test-secret-id");
        ReflectionTestUtils.setField(tencentAsrService, "secretKey", "test-secret-key");
        ReflectionTestUtils.setField(tencentAsrService, "defaultEngineModelType", "16k_zh");
    }

    @Test
    public void testGetProviderName() {
        assertEquals("tencent", tencentAsrService.getProviderName());
    }

    @Test
    public void testCheckServiceDependencies() {
        // 测试配置完整的情况
        boolean available = tencentAsrService.checkServiceDependencies(serviceContext);
        assertTrue(available);
        
        // 测试配置不完整的情况
        ReflectionTestUtils.setField(tencentAsrService, "appId", "");
        available = tencentAsrService.checkServiceDependencies(serviceContext);
        assertFalse(available);
    }

    @Test
    public void testStartRealtimeTranscription() {
        // 创建测试参数
        ActivityText.ActivityRealtimeStartArg arg = ActivityText.ActivityRealtimeStartArg.builder()
                .objectId("test-object-123")
                .sourceLanguage("cn")
                .diarizationEnabled(true)
                .translationEnabled(false)
                .build();

        // 调用方法
        AsrService.TaskResult result = tencentAsrService.startRealtimeTranscription(serviceContext, arg);

        // 验证结果
        assertNotNull(result);
        assertEquals("tencent", result.getProvider());
        assertNotNull(result.getTaskId());
        assertNotNull(result.getWsUrl());

        // 验证WSS URL格式
        assertTrue(result.getWsUrl().startsWith("wss://asr.cloud.tencent.com/asr/v2/test-app-id"));

        // 验证必选参数
        assertTrue(result.getWsUrl().contains("secretid=test-secret-id"));
        assertTrue(result.getWsUrl().contains("timestamp="));
        assertTrue(result.getWsUrl().contains("expired="));
        assertTrue(result.getWsUrl().contains("nonce="));
        assertTrue(result.getWsUrl().contains("engine_model_type=16k_zh_large"));
        assertTrue(result.getWsUrl().contains("voice_id="));
        assertTrue(result.getWsUrl().contains("voice_format=1"));
        assertTrue(result.getWsUrl().contains("signature="));

        // 验证可选参数
        assertTrue(result.getWsUrl().contains("needvad=1"));
        assertTrue(result.getWsUrl().contains("filter_dirty=0"));
        assertTrue(result.getWsUrl().contains("filter_modal=0"));
        assertTrue(result.getWsUrl().contains("filter_punc=0"));
        assertTrue(result.getWsUrl().contains("filter_empty_result=1"));
        assertTrue(result.getWsUrl().contains("convert_num_mode=1"));
        assertTrue(result.getWsUrl().contains("word_info=0"));
        assertTrue(result.getWsUrl().contains("vad_silence_time=1500")); // 自定义参数
        assertTrue(result.getWsUrl().contains("max_speak_time=60000"));
        assertTrue(result.getWsUrl().contains("noise_threshold=0"));
        assertTrue(result.getWsUrl().contains("emotion_recognition=0"));

        System.out.println("Generated WSS URL: " + result.getWsUrl());

        // 验证URL长度合理（不会太长导致问题）
        assertTrue(result.getWsUrl().length() < 2000, "URL should not be too long");
    }

    @Test
    public void testStartRealtimeTranscriptionWithoutDiarization() {
        // 创建测试参数（不启用说话人分离）
        ActivityText.ActivityRealtimeStartArg arg = ActivityText.ActivityRealtimeStartArg.builder()
                .objectId("test-object-123")
                .sourceLanguage("en")
                .diarizationEnabled(false)
                .translationEnabled(false)
                .build();

        // 调用方法
        AsrService.TaskResult result = tencentAsrService.startRealtimeTranscription(serviceContext, arg);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getWsUrl());
        
        // 验证不包含说话人分离参数
        assertFalse(result.getWsUrl().contains("needvad=1"));
        
        // 验证引擎模型类型为英文
        assertTrue(result.getWsUrl().contains("engine_model_type=16k_en"));
    }

    @Test
    public void testStopRealtimeTranscription() {
        // 创建测试参数
        ActivityText.ActivityRealtimeStopArg arg = ActivityText.ActivityRealtimeStopArg.builder()
                .objectId("test-object-123")
                .taskId("test-task-id")
                .build();

        // 调用方法
        java.util.Map<String, String> result = tencentAsrService.stopRealtimeTranscription(serviceContext, arg);

        // 验证结果
        assertNotNull(result);
        assertEquals("success", result.get("status"));
    }

    @Test
    public void testGetServiceConfig() {
        // 调用方法
        java.util.Map<String, Object> config = tencentAsrService.getServiceConfig(serviceContext);

        // 验证结果
        assertNotNull(config);
        assertEquals("tencent", config.get("provider"));
        assertNotNull(config.get("supportedLanguages"));
        assertNotNull(config.get("supportedFormats"));
        assertNotNull(config.get("engineModels"));
        assertEquals(200, config.get("maxConcurrency"));
    }

    @Test
    public void testLanguageToEngineModelMapping() {
        // 测试中文
        ActivityText.ActivityRealtimeStartArg argCn = ActivityText.ActivityRealtimeStartArg.builder()
                .sourceLanguage("cn")
                .build();
        AsrService.TaskResult resultCn = tencentAsrService.startRealtimeTranscription(serviceContext, argCn);
        assertTrue(resultCn.getWsUrl().contains("engine_model_type=16k_zh_large"));

        // 测试英文
        ActivityText.ActivityRealtimeStartArg argEn = ActivityText.ActivityRealtimeStartArg.builder()
                .sourceLanguage("en")
                .build();
        AsrService.TaskResult resultEn = tencentAsrService.startRealtimeTranscription(serviceContext, argEn);
        assertTrue(resultEn.getWsUrl().contains("engine_model_type=16k_en"));

        // 测试日文
        ActivityText.ActivityRealtimeStartArg argJa = ActivityText.ActivityRealtimeStartArg.builder()
                .sourceLanguage("ja")
                .build();
        AsrService.TaskResult resultJa = tencentAsrService.startRealtimeTranscription(serviceContext, argJa);
        assertTrue(resultJa.getWsUrl().contains("engine_model_type=16k_ja"));
    }

    @Test
    public void testConfigurationError() {
        // 清空配置
        ReflectionTestUtils.setField(tencentAsrService, "appId", "");
        ReflectionTestUtils.setField(tencentAsrService, "secretId", "");
        ReflectionTestUtils.setField(tencentAsrService, "secretKey", "");

        ActivityText.ActivityRealtimeStartArg arg = ActivityText.ActivityRealtimeStartArg.builder()
                .objectId("test-object-123")
                .sourceLanguage("cn")
                .build();

        // 验证抛出异常
        assertThrows(RuntimeException.class, () -> {
            tencentAsrService.startRealtimeTranscription(serviceContext, arg);
        });
    }
}
