package com.facishare.crm.sfa.activity.predefine.service.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 测试 SpeakerReplaceInfo 的 JSON 反序列化
 */
public class SpeakerReplaceInfoTest {

    @Test
    public void testTargetUserIdDeserialization() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        
        // 测试数字类型的 targetUserId
        String json = "{\"targetUserId\": 7590, \"targetUserName\": \"陈雷\"}";
        
        ActivityText.SpeakerReplaceInfo info = mapper.readValue(json, ActivityText.SpeakerReplaceInfo.class);
        
        // 验证 targetUserId 被正确转换为字符串
        assertEquals("7590", info.getTargetUserId());
        assertEquals("陈雷", info.getTargetUserName());
    }
    
    @Test
    public void testTargetUserIdStringDeserialization() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        
        // 测试字符串类型的 targetUserId
        String json = "{\"targetUserId\": \"7590\", \"targetUserName\": \"陈雷\"}";
        
        ActivityText.SpeakerReplaceInfo info = mapper.readValue(json, ActivityText.SpeakerReplaceInfo.class);
        
        // 验证 targetUserId 保持为字符串
        assertEquals("7590", info.getTargetUserId());
        assertEquals("陈雷", info.getTargetUserName());
    }
    
    @Test
    public void testUserIdDeserialization() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        
        // 测试数字类型的 userId
        String json = "{\"userId\": 1, \"userName\": \"1\"}";
        
        ActivityText.SpeakerReplaceInfo info = mapper.readValue(json, ActivityText.SpeakerReplaceInfo.class);
        
        // 验证 userId 被正确转换为字符串
        assertEquals("1", info.getUserId());
        assertEquals("1", info.getUserName());
    }
}
