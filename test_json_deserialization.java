import com.fasterxml.jackson.databind.ObjectMapper;

public class TestJsonDeserialization {
    public static void main(String[] args) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        
        // 模拟前端传递的 JSON 数据
        String json = "{"
            + "\"targetUserId\": 7590,"
            + "\"targetUserName\": \"陈雷\","
            + "\"userId\": 1,"
            + "\"userName\": \"1\""
            + "}";
        
        System.out.println("Original JSON: " + json);
        
        // 这里需要替换为实际的 SpeakerReplaceInfo 类
        // SpeakerReplaceInfo info = mapper.readValue(json, SpeakerReplaceInfo.class);
        
        // System.out.println("Deserialized targetUserId: '" + info.getTargetUserId() + "'");
        // System.out.println("Deserialized userId: '" + info.getUserId() + "'");
        
        System.out.println("Test completed - check if numeric values are converted to strings");
    }
}
